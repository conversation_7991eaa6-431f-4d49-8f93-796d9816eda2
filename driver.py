import os
import psutil,csv
import threading,random
import time
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium import webdriver
from selenium.webdriver.common.by import By
import chromedriver_autoinstaller
from bs4 import BeautifulSoup
from selenium.webdriver.support.ui import WebDriverWait
import pandas as pd
from selenium.webdriver.support import expected_conditions as EC
from datetime import datetime


class BeenVerifiedScraping():
    def __init__(self):
        self.chrome_options = webdriver.ChromeOptions()
        self.chrome_options.add_argument("--disable-notifications")
        self.chrome_options.add_argument("--disable-gpu")
        self.chrome_options.add_argument("--disable-popup-blocking")
        self.chrome_options.add_argument("--profile-directory=Default")
        self.chrome_options.add_argument("--ignore-certificate-errors")
        self.chrome_options.add_argument("--disable-plugins-discovery")
        self.chrome_options.add_experimental_option("debuggerAddress", "localhost:9222")

    def start_chrome(self):
        return os.system('"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222')

    def close_browser(self):
        try:
            PROCNAME = "chromedriver.exe"
            userName = os.getlogin()
            for proc in psutil.process_iter():
                if proc.name() == PROCNAME or proc.name() == 'chrome.exe':
                    if str(userName) in str(proc.username()):
                        print(str(proc.name()))
                        print(proc.username())
                        proc.kill()
        except Exception as ex:
            print(str(ex))

    def start_scraping(self):
        path = chromedriver_autoinstaller.install(cwd=True)
        print(path)
        time.sleep(2)

        self.close_browser()
        time.sleep(2)

        th = threading.Thread(target=self.start_chrome, args=())
        th.daemon = True
        th.start()
        time.sleep(3)
        driver = webdriver.Chrome(options=self.chrome_options)

if __name__ == '__main__':
    obj = BeenVerifiedScraping()
    obj.start_scraping()