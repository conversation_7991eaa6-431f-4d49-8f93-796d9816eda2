@echo off
set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
set BASE_PROFILE_PATH=C:\chromeProfile

REM === Launch Chrome with profiles 1-6 only ===

start "" %CHROME_PATH% --remote-debugging-port=9221 --user-data-dir=%BASE_PROFILE_PATH%1 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9222 --user-data-dir=%BASE_PROFILE_PATH%2 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9223 --user-data-dir=%BASE_PROFILE_PATH%3 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9224 --user-data-dir=%BASE_PROFILE_PATH%4 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9225 --user-data-dir=%BASE_PROFILE_PATH%5 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9226 --user-data-dir=%BASE_PROFILE_PATH%6 --new-window

REM === Wait a moment for Chrome profiles to fully load ===
timeout /t 5 /nobreak

REM === Open PowerShell and run PM2 with cwp.json ===
echo Starting PM2 with cwp.json configuration...
start "" powershell.exe -Command "cd 'c:\Users\<USER>\Desktop\NewCompany_Scrapers'; pm2 start cwp.json"

echo Automation complete - Chrome profiles opened and PM2 started with cwp.json
