@echo off
set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
set BASE_PROFILE_PATH=C:\chromeProfile

REM === Launch Chrome with each profile and port ===

start "" %CHROME_PATH% --remote-debugging-port=9226 --user-data-dir=%BASE_PROFILE_PATH%7 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9227 --user-data-dir=%BASE_PROFILE_PATH%8 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9228 --user-data-dir=%BASE_PROFILE_PATH%9 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9229 --user-data-dir=%BASE_PROFILE_PATH%10 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9230 --user-data-dir=%BASE_PROFILE_PATH%11 --new-window
start "" %CHROME_PATH% --remote-debugging-port=9231 --user-data-dir=%BASE_PROFILE_PATH%12 --new-window

REM === Wait a moment for Chrome profiles to fully load ===
timeout /t 5 /nobreak

REM === Open PowerShell and run PM2 with cwp.json ===
echo Starting PM2 with cwp.json configuration...
start "" powershell.exe -Command "cd 'c:\Users\<USER>\Desktop\NewCompany_Scrapers'; pm2 start cwp.json"

echo Automation complete - Chrome profiles opened and PM2 started with cwp.json
