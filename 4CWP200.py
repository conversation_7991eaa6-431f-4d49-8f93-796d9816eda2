#fawad
import subprocess
import os,re
import random
from bs4 import BeautifulSoup
from selenium.webdriver.common.keys import Keys
import csv
import pandas as pd
import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from time import sleep
import requests
defined_ranges=['0-1', '1-10','11-50','51-200','201-500']
defined_titles=['Director', 'HR', 'Head', 'CEO', 'Managing', 'Founder', 'Vice President', 'President', 'Operation', 'Executive', 'Owner', 'Manager', 'Talent', 'Consultant', 'MD', 'Co-Founder', 'Partner', 'Recruitment', 'Recruiter', 'Human Resource', 'Operations', 'People', 'VP', 'Senior', 'office', 'Recruitments', 'Recruiting', 'Recruitings', 'Candidates', 'Candidate', 'Career', 'Careers', 'Placement', 'Placements', 'Hiring', 'Hirings', 'Talents', 'workforce', 'workforces', 'recruiters', 'Principal', 'Lead', 'Cheif', 'Chief']
# Path to your chromedriver.exe
CHROME_DRIVER_PATH = "chromedriver.exe"  # Update this path

def start_chrome_with_debugging():
    chrome_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
    debugging_port = "9229"
    user_data_dir = "C:\\chromeProfile10"
    
    # Create the user data directory if it doesn't exist
    os.makedirs(user_data_dir, exist_ok=True)
    try:
        subprocess.run(
            [
                chrome_path,
                f"--remote-debugging-port={debugging_port}",
                f"--user-data-dir={user_data_dir}",
                "--start-maximized"
            ],
            check=True
        )
        print("Chrome launched with debugging.")
    except Exception as e:
        print(f"Error launching Chrome: {e}")
        return False
    
    return True

def browser():
    if not start_chrome_with_debugging():
        return None
    
    chrome_options = Options()
    chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9229")

    try:
        driver_service = Service(CHROME_DRIVER_PATH)
        driver = webdriver.Chrome(service=driver_service, options=chrome_options)
        driver.implicitly_wait(10)
        return driver
    except Exception as e:
        print(f"Error starting WebDriver: {e}")
        return None

driver = browser()

if driver:
    print("Browser launched successfully!")
else:
    print("Failed to launch browser.")
    exit(1)

# crm_url='https://api.backend.ultimateoutsourcing.co.uk/api/scrapper/'
crm_url='https://m2b3x7cl-5001.inc1.devtunnels.ms/api/scrapper/'
company_id = 0
country_id = None
sector_id = 0
user_id = 1
p_id = '3'

###################################################################
profilename = 'Fawad'
Profile_Working  = {
    "current_date": datetime.datetime.today().strftime('%Y-%m-%d'),
    # "jobs_clicked": 0,
    "Company_Extracted": 0,
    # "Profile_Extracted": 0,
}

def save_Profile_Working():
    today = datetime.datetime.today().strftime('%Y-%m-%d')
    if today != Profile_Working["current_date"]:
        # Profile_Working["jobs_clicked"] = 0
        Profile_Working["Company_Extracted"] = 0
        # Profile_Working["Profile_Extracted"] = 0
        Profile_Working["current_date"] = today
    
    filename = f"{profilename}_Profile_{today}.txt"
    if os.path.exists(filename):
        print(f"Updating existing Profile Working file for {today}")
    else:
        print(f"Creating new Profile Working  file for {today}")
    
    stats_text = f"""
Profile_Working - {today}
================================
Total Company_Extracted: {Profile_Working['Company_Extracted']}
================================
"""
    
    try:
        with open(filename, 'w') as f:
            f.write(stats_text)
        print(f"Profile Working saved to {filename}")
    except Exception as e:
        print(f"Error saving Profile Working: {e}")

####################################################################


####################################################################

CWP_Report_paypal = {                
    "date": datetime.datetime.today().strftime('%Y-%m-%d'),
    "companies_extracted": 0,
    "scrapper_profile_name": '',
	}

while True:
    try:
        driver.get('https://www.linkedin.com/home')
        sleep(3)
        login = False
        try:
            try:profileename = driver.find_element(By.XPATH,'//h3[@class="profile-card-name text-heading-large"]').text.strip()
            except: profileename = ''
            CWP_Report_paypal['scrapper_profile_name'] = f'{profileename}_{p_id}'
            try:
                checknmaepage = driver.find_element(By.XPATH,'//p[contains(@class,"identity-headline t-12 t-black--")]')
            except:
                checknmaepage = driver.find_element(By.XPATH,'//h3[@class="profile-card-name text-heading-large"]')
            print('Already login')
            login = True
        except:
            driver.get('https://www.linkedin.com/home')
            print('except working')
            sleep(3)
            try:
                signinwithemail = driver.find_element(By.XPATH,'//a[@data-test-id="home-hero-sign-in-cta"]').click()
                sleep(4)
                selectupperAccount = driver.find_element(By.XPATH,'//a[@data-cie-control-urn="sign_in_with_another_account"]').click()
                sleep(2)
                # password = driver.find_element(By.XPATH,'//input[@id="password"]').send_keys('*()!~rg$@!@,._+1fge21SD7q')
                signin = driver.find_element(By.XPATH,'//button[@aria-label="Sign in"]').click()
                login = True
            except:
                print('Error while login try again')
        if login == True:
            try:
                rs=requests.get(crm_url+'getCompanyLinkWithRegion', params={'sector_id':2, 'country_id':1 })
                print(rs.status_code)
                try:responce = rs.json()
                except: print('No data found in response')
                print(responce)
        # for c in responce:
                url = responce['company']['profile_url_encoded']
                if not url:
                    url = responce['company']['profile_url']
                print(url)
                country_id = responce['company']['countryId']
                sector_id = responce['company']['sectorId']
                print('Sector ID ------------------->',sector_id)
                user_id = responce['company']['userId']
                company_id=responce['company']['id']
                # minId=responce['id']
                # print(url)
                pattern = re.compile(r'(https://www.linkedin.com/company/[^/]+)')
                base_url = pattern.match(url).group()
                # print(base_url)
                driver.get(base_url+ '/about/')
                print('Successfully open the page')
                Profile_Working['Company_Extracted'] += 1
                CWP_Report_paypal['companies_extracted'] += 1
                report_post = requests.post('https://m2b3x7cl-5001.inc1.devtunnels.ms/api/company-scrapper',json=CWP_Report_paypal)
                print('Report Post Status Code ------------------->',report_post.status_code)
                save_Profile_Working()
                sleep(random.randint(0,60))
                current_url = driver.current_url.replace('about/','')
                try:
                    avator = driver.find_element(By.XPATH,'//div[@class="org-top-card-primary-content__logo-container"]/img').get_attribute('src')
                    # print(avator)
                except: avator = ''
                print('Avator------------------------>',avator)
                try:
                    company_name = driver.find_element(By.XPATH,'//h1[contains(@class,"ember-view org-top-card-summary__title")]').text.strip().replace('/n','')
                    # print(company_name)
                except: company_name = ''
                print('Company Name------------------->',company_name)
                try:
                    com_headline = driver.find_element(By.XPATH,'//p[@class="org-top-card-summary__tagline"]').text.strip().replace('/n','')
                    # print(com_headline)
                except: com_headline = ''
                print('Company com_headline------------------->',com_headline)
                try:
                    industry = driver.find_element(By.XPATH,'//div[@class="org-top-card-summary-info-list"]/div[@class="org-top-card-summary-info-list__info-item"][1]').text.strip().replace('/n','')
                    # print(industry)
                except: industry = ''
                print('Industry---------------------->',industry)
                try:
                    headquater = driver.find_element(By.XPATH,'//div[@class="org-top-card-summary-info-list"]/div[2]/div[1]').text.strip().replace('/n','')
                    # print(headquater)
                except: headquater = ''
                print('Headquater------------------->',headquater)
                try:city = headquater.split(',')[0]
                except: city = ''
                try:countryyy = headquater.split(',')[1]
                except: countryyy = ''
                try:
                    follower = driver.find_element(By.XPATH,'//div[@class="org-top-card-summary-info-list"]/div[2]/div[2]').text.strip().replace('/n','').replace('followers','').replace('follower','').replace('+','').replace(',','').strip()
                    # print(follower)
                    # if 'K' in follower:
                    #     follower = follower.replace('K','000').replace('.','')
                    # if 'M' in follower:
                    #     follower = follower.replace('M','000000').replace('.','')
                except:
                    try:follower = driver.find_element(By.XPATH,'//div[@class="org-top-card-summary-info-list__info-item" and contains(., "followers")]').text.strip().replace('/n','').replace('followers','').replace('follower','').replace('+','').replace(',','').strip()
                    except: follower = ''
                
                print('Successfully get the follwer information',follower)
                try:
                    website = driver.find_element(By.XPATH,'//dd//a[@class="link-without-visited-state ember-view"]').get_attribute('href')
                    # print(website)
                except:website = ''
                print('Successfully get the website information',website)
                try:
                    phone = driver.find_element(By.XPATH,'//dd//a[contains(@href,"tel:")]').get_attribute('href').replace('tel:','').replace('+','')
                    # print(phone)
                except:phone = ''
                print('Successfully get the phone information',phone)
                try:
                    associate_member = driver.find_element(By.XPATH,'//dd[@class="t-black--light mb4 text-body-medium"]/a[@class="ember-view text-body-medium t-black--light link-without-visited-state"]/span').text.strip().split('associated')[0].replace(',','').strip()
                    # print(associate_member)
                except: associate_member = ''
                print('Successfully get the associate member information',associate_member)
                try:
                    employ = driver.find_element(By.XPATH,'//div[@class="org-top-card-summary-info-list"]//a//span[contains(., "employees")]').text.strip().replace('/n','').replace('employees','').replace('employee','').replace('+','').replace(',', '').strip()
                    # except: employ = driver.find_element(By.XPATH,'//div[@class="org-top-card-summary-info-list"]//a//span[contains(., "employees")]').text.strip().replace('/n','').replace('employees','').replace('employee','').replace('+','').replace(',', '').strip()
                    if 'K' in employ:
                        employ = employ.replace('K','000').replace('.','')
                    if 'M' in employ:
                        employ = employ.replace('M','000000').replace('.','')
                    # if '-' in employ:
                    #     employ = employ.split('-')[1]
                            
                except:employ = 0
                print('Successfully get the employ information',employ)
                try: 
                    des = driver.find_element(By.XPATH,'//h2[@class="text-heading-xlarge"]/..//p[@class="break-words white-space-pre-wrap t-black--light text-body-medium"]').text.strip().replace('\n',' ').strip()
                    # print(des)
                except: des = ''
                print('Successfully get the description information',des)
                try:
                    employ_link = driver.find_element(By.XPATH,'//div[@class="org-top-card-summary-info-list"]//a').get_attribute('href')
                    # print(employ_link)
                except: employ_link = None
                print('Successfully get the employ link information',employ_link)
                try:
                    cover_img = driver.find_element(By.XPATH,'//div[@class="org-cropped-image__cover-image background-image"]').get_attribute('style') 
                    cover_img = str(cover_img).replace('background-image: url("','').replace('");','')
                    # print(cover_img)
                except: 
                    try:
                        cover_img = driver.find_element(By.XPATH,'//div[contains(@class,"pic-cropper__target-image-container")]/img').get_attribute('src')
                        # print(cover_img)
                    except:
                        cover_img = ''
                        # print('Cover not found')

                try:start_range = employ
                except: start_range = 0
                
                if start_range != 0:
                    if '-' in start_range:
                        start_range = start_range.split('-')[0].replace('alumni','')
                try:end_range = employ
                except: end_range = 0
                if end_range != 0:
                    if '-' in end_range:
                        end_range = end_range.split('-')[1]
                    else:
                        end_range = 200
                try:
                    founderd= driver.find_element(By.XPATH,'//h3[text()="Founded"]/../following-sibling::dd').text.strip().replace('/n','')
                    # print(founderd)
                except: founderd = ''
                
                try:
                    specility = driver.find_element(By.XPATH,'//h3[text()="Specialties"]/../following-sibling::dd').text.strip().replace('/n','')
                    # print(specility)
                except: specility = ''
                print('Successfully get the company information')
                # try: 
                #     location = driver.find_element(By.XPATH,'//div[@class="org-location-card pv2"]/p').text.strip().replace('/n','')
                #     # print(location)
                # except: location = ''
                print('City ---------------->>',city,'Country  ----------------->>>',countryyy)
                print('Successfully get the company information')
                try:company_public_id=base_url.split('/')[4]
                except: company_public_id = ''

                if industry and ('Human Resources Services' in industry or 'Staffing and Recruiting' in industry or 'Executive Search Services' in industry):
                    sector_id=2
                else:
                    defined_keywords_for_sr = [ 'Recruitment', 'Recruitments', 'Recruiting', 'Recruitings', 'Candidates', 'Candidate', 'Career', 'Careers', 'Placement', 'Placements', 'Hiring', 'Hirings', 'Talents', 'Talent', 'workforce', 'workforces', 'recruiters', 'recruiter', 'placement', 'placements']
                    if any(keyword in company_name for keyword in defined_keywords_for_sr) :
                        sector_id=2
                    else:
                        sector_id=1



                company_model = {
                    "id": int(company_id),
                    "public_id": str(company_public_id),
                    "profile_url": str(current_url),
                    "profile_url_encoded": str(url),
                    "logo": str(avator),
                    "cover": str(cover_img),
                    "phone_number": str(phone),
                    "website": str(website),
                    "tagline": str(com_headline),
                    "staff_count": str(associate_member),
                    "staff_count_range_start": int(start_range),
                    "staff_count_range_end": int(end_range),
                    "follower_count": str(follower),
                    "actual_range": str(employ),
                    "description": str(des),
                    "founded_on": str(founderd),
                    "headquarter_country": str(headquater),
                    "headquarter_city": str(city),
                    "headquarter_geographic_area": "",
                    "headquarter_line1": "",
                    "headquarter_line2": "",
                    "headquarter_postal_code": "",
                    "industry": str(industry),
                    "specialities": str(specility),
                    "company_email": "",
                    "is_scrapped_fully": True,
                    "scrapper_level": 3,
                    "scrapper_profile_name":profileename,
                    "sector_id":int(sector_id),
                    }
                print('Company Payload Json ----->>>  ',company_model)
                try:
                    add_company = requests.post(crm_url+'updateCompanyByScrapper',json=company_model)
                    # print('Create Company -----------------------------------------------------------', add_company.json())
                    api_status=add_company.status_code
                    print(api_status)
                    print('Company Payload out ------------>>>>> ',add_company.json())
                    # country_id  = add_company.json()['country_id']
                    print(country_id)
                    if api_status==200 or api_status==201:
                        print('Create Company -----------------------------------------------------------', add_company.json())
                        country_id = add_company.json()['company']['countryId']
                        # company_id = add_company.json()['id']

                    else:
                        print(add_company.json())
                        print('error occured on uploading in company information')
                except:
                    print('error occured on uploading in company information')

                # people part--------------------------------------2-----------------------------
                
                print('Employ--------------------->',employ)
                print('Associate Member-----New---------------->',associate_member)
                
                if int(associate_member) < 500:
                    print('Employee is less than 200')
                    people_page = base_url+'/people/'
                    driver.get(people_page)
                    sleep(5)
                    while True:
                        # scroll down 
                        body = driver.find_element(By.TAG_NAME,'body')
                        for _ in range(80): 
                            body.send_keys(Keys.PAGE_DOWN)
                            sleep(1)
                        try:
                            load_more = driver.find_element(By.XPATH,'//button[@class="artdeco-button artdeco-button--muted artdeco-button--1 artdeco-button--full artdeco-button--secondary ember-view scaffold-finite-scroll__load-button"]').click()
                            print('Successfully click on Load More page')
                        except:
                            print('reached at last page')
                            break

                    print('-----------------------Out of loop-2-----------------------')
                    html_content = driver.page_source
                    soup = BeautifulSoup(html_content, 'html.parser')
                    boxes = elements = soup.select('section[class*="artdeco-card"][class*="full-width"]')
                    print('----------------------> Total people found  ---> 2', len(boxes))
                    for box in boxes:
                        try:
                            people_name = box.select_one('div.artdeco-entity-lockup__title.ember-view').get_text(strip=True).replace('\n', ' ')
                            # print(people_name)
                        except:
                            people_name = ''
                        try:
                            people_title = box.select_one('div.artdeco-entity-lockup__subtitle.ember-view').get_text(strip=True).replace('\n', ' ')
                            # print(people_title)
                        except:
                            people_title = ''
                        if 'LinkedIn Member' in people_name:
                            print('LinkedIn member found')
                            print('Skip this record')
                        else: #any(title in people_title for title in defined_titles):
                            print('Not a LinkedIn member')
                            try:
                                people_url = box.select_one('div.artdeco-entity-lockup__title.ember-view a')['href']
                                people_url = str(people_url).split('?')[0]
                            except:
                                people_url = ''
                            try:
                                people_headline = box.select_one('span.text-align-center').get_text(strip=True).replace('\n', ' ')
                                # print(people_headline)
                            except:
                                people_headline = ''
                            try:
                                people_avator = box.select_one('div.artdeco-entity-lockup__image.artdeco-entity-lockup__image--type-circle.ember-view a img')['src']
                                # print(people_avator)
                            except:
                                people_avator = ''
                            try:
                                first_name2 = people_name.split(' ')[0]
                            except:
                                first_name2 = ''
                            try:
                                sec_name2 = people_name.split(' ')[1]
                            except:
                                sec_name2 = ''
                            
                            print(people_url, people_name)
                            people_model = {
                                "profile_url":str(people_url),
                                "full_name":str(people_name),
                                "first_name":str(first_name2),
                                "last_name":str(sec_name2),
                                "avator":str(people_avator),
                                "headline":str(people_headline),
                                "current_title":str(people_title),
                                "industry":None,
                                "SR_specied_industry":None,
                                "summary":None,
                                "date_of_birth":None,
                                "company_id":int(company_id),
                                "user_id":user_id,
                                "sector_id":sector_id,
                                "country_id":country_id,
                                "scrapper_profile_name":profileename,
                            }
                            print(people_model)
                            try:
                                add_person = requests.post(crm_url+'addPersonOnly',json=people_model)
                                person_api_status=add_person.status_code
                                print(person_api_status)
                                print(add_person.json())
                                if person_api_status==200 or person_api_status==201:
                                    print('Create Person -----------------------------------------------------------', add_person.json())
                                else:
                                    print(add_person.json())
                                    print('error occured on uploading in person information')
                            except:
                                print('error occured on uploading in person information')
                else:
                    print('Employee is more than 200')
            except Exception as e:
                print('Error while getting company link:', e)
                sleep(10)
        else:
            print('Error while login')
    except:
        print('Error while getting company link')